"""
DataFetcher Agent for AI Stock Research Assistant

This module contains the DataFetcher agent that retrieves financial data
using the yfinance library. It fetches key metrics like current price,
PE ratio, market cap, and revenue growth.
"""

import yfinance as yf
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import json


class DataFetcher:
    """
    Agent responsible for fetching financial data from Yahoo Finance
    """
    
    def __init__(self):
        self.name = "DataFetcher"
        self.description = "Fetches financial data for stocks using Yahoo Finance API"
    
    def fetch_stock_data(self, ticker: str) -> Dict[str, Any]:
        """
        Fetch comprehensive stock data for a given ticker
        
        Args:
            ticker (str): Stock ticker symbol (e.g., 'AAPL', 'MSFT')
            
        Returns:
            Dict containing financial metrics and data
        """
        try:
            # Create yfinance Ticker object
            stock = yf.Ticker(ticker.upper())
            
            # Get stock info
            info = stock.info
            
            # Get historical data for calculations
            hist = stock.history(period="2y")  # 2 years of data
            
            # Get financials for revenue growth calculation
            financials = stock.financials
            
            # Extract key metrics
            data = {
                "ticker": ticker.upper(),
                "company_name": info.get("longName", "N/A"),
                "sector": info.get("sector", "N/A"),
                "industry": info.get("industry", "N/A"),
                "current_price": info.get("currentPrice", info.get("regularMarketPrice", "N/A")),
                "market_cap": info.get("marketCap", "N/A"),
                "pe_ratio": info.get("trailingPE", "N/A"),
                "forward_pe": info.get("forwardPE", "N/A"),
                "price_to_book": info.get("priceToBook", "N/A"),
                "dividend_yield": info.get("dividendYield", "N/A"),
                "beta": info.get("beta", "N/A"),
                "52_week_high": info.get("fiftyTwoWeekHigh", "N/A"),
                "52_week_low": info.get("fiftyTwoWeekLow", "N/A"),
                "volume": info.get("volume", "N/A"),
                "avg_volume": info.get("averageVolume", "N/A"),
                "revenue_growth": self._calculate_revenue_growth(financials),
                "price_performance": self._calculate_price_performance(hist),
                "analyst_recommendation": info.get("recommendationMean", "N/A"),
                "target_price": info.get("targetMeanPrice", "N/A"),
                "fetch_timestamp": datetime.now().isoformat()
            }
            
            # Format large numbers for readability
            data = self._format_financial_data(data)
            
            return {
                "success": True,
                "data": data,
                "message": f"Successfully fetched data for {ticker.upper()}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "message": f"Error fetching data for {ticker}: {str(e)}"
            }
    
    def _calculate_revenue_growth(self, financials: pd.DataFrame) -> str:
        """Calculate year-over-year revenue growth"""
        try:
            if financials.empty or 'Total Revenue' not in financials.index:
                return "N/A"
            
            revenue_data = financials.loc['Total Revenue'].dropna()
            if len(revenue_data) < 2:
                return "N/A"
            
            # Get the two most recent years
            recent_revenue = revenue_data.iloc[0]  # Most recent
            previous_revenue = revenue_data.iloc[1]  # Previous year
            
            growth_rate = ((recent_revenue - previous_revenue) / previous_revenue) * 100
            return f"{growth_rate:.2f}%"
            
        except Exception:
            return "N/A"
    
    def _calculate_price_performance(self, hist: pd.DataFrame) -> Dict[str, str]:
        """Calculate price performance over different periods"""
        try:
            if hist.empty:
                return {"1M": "N/A", "3M": "N/A", "6M": "N/A", "1Y": "N/A"}
            
            current_price = hist['Close'].iloc[-1]
            performance = {}
            
            periods = {
                "1M": 30,
                "3M": 90,
                "6M": 180,
                "1Y": 365
            }
            
            for period_name, days in periods.items():
                try:
                    if len(hist) > days:
                        past_price = hist['Close'].iloc[-days]
                        perf = ((current_price - past_price) / past_price) * 100
                        performance[period_name] = f"{perf:.2f}%"
                    else:
                        performance[period_name] = "N/A"
                except:
                    performance[period_name] = "N/A"
            
            return performance
            
        except Exception:
            return {"1M": "N/A", "3M": "N/A", "6M": "N/A", "1Y": "N/A"}
    
    def _format_financial_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format large financial numbers for better readability"""
        
        def format_large_number(value):
            if isinstance(value, (int, float)) and value != "N/A":
                if value >= 1e12:
                    return f"${value/1e12:.2f}T"
                elif value >= 1e9:
                    return f"${value/1e9:.2f}B"
                elif value >= 1e6:
                    return f"${value/1e6:.2f}M"
                elif value >= 1e3:
                    return f"${value/1e3:.2f}K"
                else:
                    return f"${value:.2f}"
            return value
        
        # Format market cap
        if data.get("market_cap") != "N/A":
            data["market_cap"] = format_large_number(data["market_cap"])
        
        # Format current price
        if data.get("current_price") != "N/A":
            data["current_price"] = f"${data['current_price']:.2f}"
        
        # Format 52-week high/low
        for key in ["52_week_high", "52_week_low", "target_price"]:
            if data.get(key) != "N/A":
                data[key] = f"${data[key]:.2f}"
        
        # Format dividend yield
        if data.get("dividend_yield") != "N/A":
            data["dividend_yield"] = f"{data['dividend_yield']*100:.2f}%"
        
        return data
    
    def get_formatted_summary(self, ticker: str) -> str:
        """
        Get a formatted text summary of stock data for use by other agents
        
        Args:
            ticker (str): Stock ticker symbol
            
        Returns:
            Formatted string summary of financial data
        """
        result = self.fetch_stock_data(ticker)
        
        if not result["success"]:
            return f"Error: {result['message']}"
        
        data = result["data"]
        
        summary = f"""
FINANCIAL DATA SUMMARY FOR {data['ticker']}
{'='*50}

Company: {data['company_name']}
Sector: {data['sector']} | Industry: {data['industry']}

VALUATION METRICS:
- Current Price: {data['current_price']}
- Market Cap: {data['market_cap']}
- P/E Ratio: {data['pe_ratio']}
- Forward P/E: {data['forward_pe']}
- Price-to-Book: {data['price_to_book']}
- Beta: {data['beta']}

PERFORMANCE:
- 52-Week High: {data['52_week_high']}
- 52-Week Low: {data['52_week_low']}
- 1-Month Performance: {data['price_performance']['1M']}
- 3-Month Performance: {data['price_performance']['3M']}
- 6-Month Performance: {data['price_performance']['6M']}
- 1-Year Performance: {data['price_performance']['1Y']}

GROWTH & INCOME:
- Revenue Growth (YoY): {data['revenue_growth']}
- Dividend Yield: {data['dividend_yield']}

ANALYST DATA:
- Recommendation Score: {data['analyst_recommendation']} (1=Strong Buy, 5=Strong Sell)
- Target Price: {data['target_price']}

TRADING DATA:
- Volume: {data['volume']:,} shares
- Average Volume: {data['avg_volume']:,} shares

Data fetched at: {data['fetch_timestamp']}
        """
        
        return summary.strip()
