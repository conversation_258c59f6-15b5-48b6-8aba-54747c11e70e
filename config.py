"""
Configuration module for AI Stock Research Assistant

This module handles all configuration settings including:
- OpenAI API configuration
- AutoGen settings
- LLM model configurations
"""

import os
from dotenv import load_dotenv
from typing import Dict, Any, Optional

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the AI Stock Research Assistant"""
    
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.azure_openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")
        self.azure_openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self.azure_openai_api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview")
        self.local_llm_base_url = os.getenv("LOCAL_LLM_BASE_URL")
        self.local_llm_model = os.getenv("LOCAL_LLM_MODEL", "llama2")
        self.autogen_use_docker = os.getenv("AUTOGEN_USE_DOCKER", "False").lower() == "true"
    
    def get_llm_config(self) -> Dict[str, Any]:
        """
        Get LLM configuration for AutoGen agents
        
        Returns:
            Dict containing LLM configuration
        """
        if self.azure_openai_api_key and self.azure_openai_endpoint:
            # Azure OpenAI configuration
            return {
                "config_list": [{
                    "model": "gpt-4",
                    "api_type": "azure",
                    "api_key": self.azure_openai_api_key,
                    "base_url": self.azure_openai_endpoint,
                    "api_version": self.azure_openai_api_version
                }],
                "temperature": 0.1,
                "timeout": 120,
            }
        elif self.local_llm_base_url:
            # Local LLM configuration (e.g., Ollama)
            return {
                "config_list": [{
                    "model": self.local_llm_model,
                    "base_url": self.local_llm_base_url,
                    "api_key": "dummy"  # Required but not used for local LLMs
                }],
                "temperature": 0.1,
                "timeout": 120,
            }
        elif self.openai_api_key:
            # OpenAI configuration
            return {
                "config_list": [{
                    "model": "gpt-4",
                    "api_key": self.openai_api_key
                }],
                "temperature": 0.1,
                "timeout": 120,
            }
        else:
            raise ValueError(
                "No valid LLM configuration found. Please set OPENAI_API_KEY, "
                "Azure OpenAI credentials, or local LLM settings in your .env file."
            )
    
    def validate_config(self) -> bool:
        """
        Validate that required configuration is present
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            self.get_llm_config()
            return True
        except ValueError:
            return False

# Global configuration instance
config = Config()
